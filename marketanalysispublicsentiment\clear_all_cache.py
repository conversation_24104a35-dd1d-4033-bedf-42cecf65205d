#!/usr/bin/env python3
"""
Comprehensive Cache Clearing Script for Financial Sentiment Analyzer

This script clears ALL cached data including:
- Company names cache
- News data cache  
- Earnings data cache
- Price data cache
- Market data cache
- Python bytecode cache (__pycache__)
- Request logs

Usage:
    python clear_all_cache.py
    
Or with confirmation prompts:
    python clear_all_cache.py --interactive
"""

import os
import shutil
import glob
import argparse
from pathlib import Path

def clear_cache_directory():
    """Clear the entire cache directory"""
    cache_dir = Path("cache")
    
    if cache_dir.exists():
        print(f"🗑️  Clearing cache directory: {cache_dir}")
        
        # Count files before deletion
        cache_files = list(cache_dir.glob("*"))
        file_count = len(cache_files)
        
        if file_count > 0:
            print(f"   Found {file_count} cache files:")
            
            # Group files by type for better reporting
            file_types = {}
            for file_path in cache_files:
                if file_path.is_file():
                    # Extract cache type from filename
                    name = file_path.name
                    if name.startswith('company_names_'):
                        cache_type = 'Company Names'
                    elif name.startswith('news_'):
                        cache_type = 'News Data'
                    elif name.startswith('earnings_'):
                        cache_type = 'Earnings Data'
                    elif name.startswith('prices_'):
                        cache_type = 'Price Data'
                    elif name.startswith('market_data_'):
                        cache_type = 'Market Data'
                    elif name == 'request_log.json':
                        cache_type = 'Request Logs'
                    else:
                        cache_type = 'Other'
                    
                    file_types[cache_type] = file_types.get(cache_type, 0) + 1
            
            # Report file types
            for cache_type, count in file_types.items():
                print(f"   - {cache_type}: {count} files")
            
            # Remove the entire directory
            shutil.rmtree(cache_dir)
            print(f"   ✅ Successfully removed {file_count} cache files")
        else:
            print("   📁 Cache directory is empty")
            cache_dir.rmdir()
    else:
        print("📁 No cache directory found")

def clear_pycache():
    """Clear Python bytecode cache (__pycache__ directories)"""
    print("\n🐍 Clearing Python bytecode cache...")
    
    pycache_dirs = []
    
    # Find all __pycache__ directories
    for root, dirs, files in os.walk("."):
        if "__pycache__" in dirs:
            pycache_path = os.path.join(root, "__pycache__")
            pycache_dirs.append(pycache_path)
    
    if pycache_dirs:
        print(f"   Found {len(pycache_dirs)} __pycache__ directories")
        
        for pycache_dir in pycache_dirs:
            try:
                # Count .pyc files
                pyc_files = glob.glob(os.path.join(pycache_dir, "*.pyc"))
                pyc_count = len(pyc_files)
                
                shutil.rmtree(pycache_dir)
                print(f"   ✅ Removed {pycache_dir} ({pyc_count} .pyc files)")
            except Exception as e:
                print(f"   ❌ Failed to remove {pycache_dir}: {e}")
    else:
        print("   📁 No __pycache__ directories found")

def clear_temp_files():
    """Clear temporary files that might be created during analysis"""
    print("\n🧹 Clearing temporary files...")
    
    temp_patterns = [
        "*.tmp",
        "*.temp", 
        "*.log",
        ".DS_Store",
        "Thumbs.db"
    ]
    
    temp_files_found = []
    
    for pattern in temp_patterns:
        files = glob.glob(pattern)
        temp_files_found.extend(files)
    
    if temp_files_found:
        print(f"   Found {len(temp_files_found)} temporary files")
        for temp_file in temp_files_found:
            try:
                os.remove(temp_file)
                print(f"   ✅ Removed {temp_file}")
            except Exception as e:
                print(f"   ❌ Failed to remove {temp_file}: {e}")
    else:
        print("   📁 No temporary files found")

def get_cache_size_info():
    """Get information about cache size before clearing"""
    cache_dir = Path("cache")
    
    if not cache_dir.exists():
        return "No cache directory found"
    
    total_size = 0
    file_count = 0
    
    for file_path in cache_dir.rglob("*"):
        if file_path.is_file():
            total_size += file_path.stat().st_size
            file_count += 1
    
    # Convert bytes to human readable format
    if total_size < 1024:
        size_str = f"{total_size} bytes"
    elif total_size < 1024 * 1024:
        size_str = f"{total_size / 1024:.1f} KB"
    elif total_size < 1024 * 1024 * 1024:
        size_str = f"{total_size / (1024 * 1024):.1f} MB"
    else:
        size_str = f"{total_size / (1024 * 1024 * 1024):.1f} GB"
    
    return f"{file_count} files, {size_str}"

def main():
    parser = argparse.ArgumentParser(description="Clear all cache data for Financial Sentiment Analyzer")
    parser.add_argument("--interactive", "-i", action="store_true", 
                       help="Ask for confirmation before clearing each cache type")
    parser.add_argument("--dry-run", "-d", action="store_true",
                       help="Show what would be deleted without actually deleting")
    
    args = parser.parse_args()
    
    print("🧹 Financial Sentiment Analyzer - Cache Clearing Tool")
    print("=" * 60)
    
    # Show current cache info
    cache_info = get_cache_size_info()
    print(f"📊 Current cache status: {cache_info}")
    
    if args.dry_run:
        print("\n🔍 DRY RUN MODE - No files will be deleted")
    
    print()
    
    if args.interactive:
        print("Interactive mode enabled - you will be prompted for each operation")
        print()
    
    # Clear cache directory
    if args.interactive:
        response = input("Clear cache directory? (y/N): ").lower().strip()
        if response in ['y', 'yes']:
            if not args.dry_run:
                clear_cache_directory()
            else:
                print("🔍 Would clear cache directory")
    else:
        if not args.dry_run:
            clear_cache_directory()
        else:
            print("🔍 Would clear cache directory")
    
    # Clear Python cache
    if args.interactive:
        response = input("Clear Python bytecode cache (__pycache__)? (y/N): ").lower().strip()
        if response in ['y', 'yes']:
            if not args.dry_run:
                clear_pycache()
            else:
                print("🔍 Would clear Python bytecode cache")
    else:
        if not args.dry_run:
            clear_pycache()
        else:
            print("🔍 Would clear Python bytecode cache")
    
    # Clear temporary files
    if args.interactive:
        response = input("Clear temporary files? (y/N): ").lower().strip()
        if response in ['y', 'yes']:
            if not args.dry_run:
                clear_temp_files()
            else:
                print("🔍 Would clear temporary files")
    else:
        if not args.dry_run:
            clear_temp_files()
        else:
            print("🔍 Would clear temporary files")
    
    print("\n" + "=" * 60)
    if args.dry_run:
        print("🔍 DRY RUN COMPLETE - No files were actually deleted")
    else:
        print("✅ Cache clearing complete!")
        print("\n💡 Benefits of clearing cache:")
        print("   - Fresh data will be fetched on next run")
        print("   - Resolves any corrupted cache issues")
        print("   - Frees up disk space")
        print("   - Ensures latest company names and data")
        
        print("\n⚠️  Note: Next run may be slower as cache rebuilds")

if __name__ == "__main__":
    main()
