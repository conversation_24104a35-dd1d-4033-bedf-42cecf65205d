#!/usr/bin/env python3
"""
Test script to explore earnings data available through yfinance
"""

import yfinance as yf
import pandas as pd
from datetime import datetime

def test_earnings_data():
    """Test what earnings data is available for a sample ticker"""
    
    # Test with Apple as a sample
    ticker = "AAPL"
    print(f"🔍 Testing earnings data for {ticker}...")
    
    try:
        stock = yf.Ticker(ticker)
        
        # Check what attributes are available
        print("\n📊 Available attributes:")
        attributes = [attr for attr in dir(stock) if not attr.startswith('_')]
        for attr in sorted(attributes):
            print(f"  - {attr}")
        
        # Test quarterly earnings
        print(f"\n📈 Quarterly Earnings for {ticker}:")
        try:
            quarterly_earnings = stock.quarterly_earnings
            if quarterly_earnings is not None and not quarterly_earnings.empty:
                print("✅ Quarterly earnings data available!")
                print(quarterly_earnings.head())
                print(f"Shape: {quarterly_earnings.shape}")
                print(f"Columns: {list(quarterly_earnings.columns)}")
            else:
                print("❌ No quarterly earnings data")
        except Exception as e:
            print(f"❌ Error getting quarterly earnings: {e}")
        
        # Test annual earnings
        print(f"\n📅 Annual Earnings for {ticker}:")
        try:
            annual_earnings = stock.earnings
            if annual_earnings is not None and not annual_earnings.empty:
                print("✅ Annual earnings data available!")
                print(annual_earnings.head())
                print(f"Shape: {annual_earnings.shape}")
                print(f"Columns: {list(annual_earnings.columns)}")
            else:
                print("❌ No annual earnings data")
        except Exception as e:
            print(f"❌ Error getting annual earnings: {e}")
        
        # Test financial statements
        print(f"\n💰 Financial Statements for {ticker}:")
        try:
            # Quarterly financials
            quarterly_financials = stock.quarterly_financials
            if quarterly_financials is not None and not quarterly_financials.empty:
                print("✅ Quarterly financials available!")
                print(f"Shape: {quarterly_financials.shape}")
                print(f"Index (metrics): {list(quarterly_financials.index[:10])}")  # First 10 metrics
                print(f"Columns (dates): {list(quarterly_financials.columns)}")
                
                # Look for earnings-related metrics
                earnings_metrics = [idx for idx in quarterly_financials.index if 'earning' in idx.lower() or 'income' in idx.lower() or 'revenue' in idx.lower()]
                print(f"📊 Earnings-related metrics found: {earnings_metrics}")
                
            else:
                print("❌ No quarterly financials data")
        except Exception as e:
            print(f"❌ Error getting quarterly financials: {e}")
        
        # Test info for basic financial metrics
        print(f"\n📋 Basic Info for {ticker}:")
        try:
            info = stock.info
            earnings_keys = [key for key in info.keys() if 'earning' in key.lower() or 'eps' in key.lower() or 'revenue' in key.lower()]
            print(f"📊 Earnings-related info keys: {earnings_keys}")
            
            for key in earnings_keys[:10]:  # Show first 10
                print(f"  {key}: {info.get(key)}")
                
        except Exception as e:
            print(f"❌ Error getting info: {e}")
            
    except Exception as e:
        print(f"❌ Error creating ticker object: {e}")

def test_multiple_tickers():
    """Test earnings data for multiple tickers"""
    test_tickers = ['AAPL', 'MSFT', 'GOOGL', 'TSLA', 'JPM']
    
    print(f"\n🔍 Testing earnings availability for multiple tickers...")
    
    for ticker in test_tickers:
        try:
            stock = yf.Ticker(ticker)
            
            # Quick check for quarterly earnings
            quarterly_earnings = stock.quarterly_earnings
            has_quarterly = quarterly_earnings is not None and not quarterly_earnings.empty
            
            # Quick check for financials
            quarterly_financials = stock.quarterly_financials
            has_financials = quarterly_financials is not None and not quarterly_financials.empty
            
            print(f"  {ticker}: Quarterly Earnings: {'✅' if has_quarterly else '❌'}, Financials: {'✅' if has_financials else '❌'}")
            
        except Exception as e:
            print(f"  {ticker}: ❌ Error - {e}")

if __name__ == "__main__":
    test_earnings_data()
    test_multiple_tickers()
