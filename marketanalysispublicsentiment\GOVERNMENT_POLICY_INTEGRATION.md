# Government Policy Integration for Financial Sentiment Analyzer

## Overview

The financial sentiment analyzer has been enhanced with government policy news analysis to provide a more comprehensive view of market conditions. This integration fetches and analyzes news from key government sources to assess policy impact on financial markets.

## New Features

### 1. Government News Sources
- **Federal Reserve Press Releases**: All Fed announcements
- **Fed Monetary Policy**: FOMC statements and policy decisions
- **Fed Speeches & Testimony**: Fed officials' public statements
- **Fed Banking Regulation**: Banking sector regulatory updates
- **Fed Enforcement Actions**: Regulatory enforcement news

### 2. Policy Impact Classification
The system automatically classifies policy news based on potential market impact:

- **High Impact**: Interest rates, monetary policy, economic outlook
- **Medium Impact**: Banking regulation, financial stability measures
- **Sector Specific**: Industry-specific policies and regulations

### 3. Enhanced Sentiment Analysis
- **Weighted Sentiment**: Policy news sentiment is weighted by impact potential
- **Combined Analysis**: Market sentiment (70%) + Policy sentiment (30%)
- **Policy Categories**: Separate analysis for monetary policy vs regulatory news

### 4. New Display Sections

#### Government Policy Analysis
- Overall policy sentiment and mood
- Breakdown by policy categories
- Total policy articles processed

#### High Impact Policy News
- Top 3 most impactful policy announcements
- Impact scores and sentiment analysis
- Clickable links to original sources

#### Combined Market & Policy Analysis
- Integrated sentiment scoring
- Policy influence assessment
- Enhanced recommendations

## Technical Implementation

### Dependencies Added
```python
import feedparser  # RSS feed parsing
import requests    # HTTP requests
import xml.etree.ElementTree as ET  # XML parsing
from urllib.parse import urljoin  # URL handling
import re  # Regular expressions
```

### Key Functions Added

1. **`fetch_government_rss_feed(feed_info)`**
   - Fetches news from individual government RSS feeds
   - Handles date parsing and timezone conversion
   - Returns structured article data

2. **`fetch_government_news_parallel()`**
   - Parallel processing of multiple government sources
   - Deduplication of articles
   - Progress tracking

3. **`classify_policy_impact(article)`**
   - Analyzes article content for policy keywords
   - Calculates impact scores
   - Classifies impact levels (High/Medium/Low/Minimal)

4. **`analyze_policy_sentiment(government_articles)`**
   - Sentiment analysis with impact weighting
   - Category-based analysis
   - High-impact article identification

5. **`analyze_market_health_optimized()` (Enhanced)**
   - Now includes policy sentiment in recommendations
   - Combined scoring methodology
   - Policy influence indicators

### Configuration

Policy keywords are categorized by impact level:

```python
POLICY_KEYWORDS = {
    'high_impact': [
        'interest rate', 'federal funds rate', 'monetary policy',
        'quantitative easing', 'inflation target', 'recession',
        'economic outlook', 'gdp growth', 'unemployment rate',
        'fomc', 'rate hike', 'rate cut', 'dovish', 'hawkish'
    ],
    'medium_impact': [
        'banking regulation', 'stress test', 'capital requirements',
        'liquidity', 'financial stability', 'systemic risk',
        'basel', 'dodd-frank', 'consumer protection', 'enforcement action'
    ],
    'sector_specific': [
        'energy policy', 'healthcare reform', 'tax policy',
        'trade policy', 'infrastructure', 'climate policy',
        'technology regulation', 'antitrust', 'merger', 'acquisition'
    ]
}
```

## Usage

The enhanced analyzer runs automatically with the existing command:

```bash
python financial_analyzer_optimized.py
```

## Output Enhancements

### New Sections in Output:
1. **Government Policy Analysis**: Overall policy sentiment and breakdown
2. **High Impact Policy News**: Most significant policy announcements
3. **Combined Analysis**: Integrated market and policy assessment
4. **Enhanced Recommendations**: Policy-informed trading recommendations

### Sample Output:
```
🏛️ GOVERNMENT POLICY ANALYSIS
  Policy Sentiment: Mildly Supportive (+0.056)
  Total Policy Articles: 24
  Policy Categories:
    🟢 Monetary Policy: +0.055 (15 articles)
    🟢 Regulatory: +0.057 (9 articles)

⚡ HIGH IMPACT POLICY NEWS:
  1. 🔥 High Impact - Score: 4.20
     Sentiment: +0.218 | Weighted: +0.676
     Source: Fed Speeches & Testimony
     [6 days ago]: "Economic Outlook and Monetary Policy"

🎯 COMBINED MARKET & POLICY ANALYSIS:
  Market Sentiment: +0.145
  Policy Influence: +0.056
  Combined Score: +0.118
  Policy Assessment: 🟡 Government policies are mildly supportive

🚀 RECOMMENDATION:
  STRONG BUY (Policy Neutral)
```

## Benefits

1. **More Comprehensive Analysis**: Incorporates government policy impact
2. **Early Warning System**: Identifies policy changes before market reaction
3. **Enhanced Accuracy**: Policy-weighted sentiment provides better predictions
4. **Real-time Updates**: Continuous monitoring of government sources
5. **Actionable Insights**: Clear policy influence indicators

## Future Enhancements

Potential additions for future versions:
- SEC press releases (when RSS feeds are available)
- Treasury Department announcements
- Congressional hearing transcripts
- International central bank communications
- Economic data release calendars

## Installation Requirements

To use the enhanced version, ensure you have the required dependency:

```bash
pip install feedparser
```

All other dependencies (requests, xml.etree.ElementTree, urllib.parse, re) are part of Python's standard library.
