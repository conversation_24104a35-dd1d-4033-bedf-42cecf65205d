#!/usr/bin/env python3

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from data_fetcher import get_time_ago
from datetime import datetime
import pytz

# Test the get_time_ago function with various inputs
print("🔍 Testing get_time_ago function...")

# Test 1: Current time
now = datetime.now()
print(f"Test 1 - Current time (no timezone): {get_time_ago(now)}")

# Test 2: Current time with UTC timezone
utc = pytz.UTC
now_utc = utc.localize(datetime.utcnow())
print(f"Test 2 - Current time (UTC): {get_time_ago(now_utc)}")

# Test 3: 2 hours ago
two_hours_ago = datetime.now() - pytz.timezone('America/Chicago').localize(datetime.now()).utcoffset() + pytz.timezone('UTC').localize(datetime.utcnow()).utcoffset()
print(f"Test 3 - Complex time: {get_time_ago(two_hours_ago)}")

# Test 4: Simple 2 hours ago
from datetime import timedelta
simple_two_hours_ago = datetime.now() - timedelta(hours=2)
print(f"Test 4 - 2 hours ago (no timezone): {get_time_ago(simple_two_hours_ago)}")

# Test 5: Test with a string that might be causing issues
try:
    # This should fail
    result = get_time_ago("not a datetime")
    print(f"Test 5 - String input: {result}")
except Exception as e:
    print(f"Test 5 - String input failed as expected: {e}")

print("\n🔍 Testing actual news fetching...")

# Import and test the actual news fetching
from data_fetcher import fetch_news_for_ticker

try:
    news = fetch_news_for_ticker("AAPL")
    print(f"Found {len(news)} articles for AAPL")
    
    for i, article in enumerate(news[:3]):  # Show first 3 articles
        print(f"\nArticle {i+1}:")
        print(f"  Title: {article.get('title', 'No title')[:50]}...")
        print(f"  Published: {article.get('published', 'No published date')}")
        print(f"  Time ago: {article.get('time_ago', 'No time_ago field')}")
        
        # Check if there's a datetime object we can debug
        if 'published' in article:
            try:
                # Try to parse the published date
                pub_date = article['published']
                print(f"  Published type: {type(pub_date)}")
                if hasattr(pub_date, 'strftime'):
                    print(f"  Published formatted: {pub_date.strftime('%Y-%m-%d %H:%M:%S')}")
                    # Test get_time_ago with this date
                    time_ago_result = get_time_ago(pub_date)
                    print(f"  get_time_ago result: {time_ago_result}")
            except Exception as e:
                print(f"  Error processing published date: {e}")

except Exception as e:
    print(f"Error fetching news: {e}")
