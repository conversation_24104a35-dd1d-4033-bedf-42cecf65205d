# Financial Sentiment Analyzer - Usage Guide

## Quick Start

### 1. Installation
```bash
# Install dependencies
pip install -r requirements.txt

# Test the installation
python test_modules.py
```

### 2. Basic Usage
```bash
# Full analysis (recommended for first run)
python financial_analyzer.py

# Quick analysis (faster, fewer sources)
python financial_analyzer.py --quick

# Show help
python financial_analyzer.py --help
```

## Command Line Options

### Analysis Modes
- `--market-only`: Analyze only stock market sentiment
- `--policy-only`: Analyze only government policy impact
- Default: Both market and policy analysis

### Display Options
- `--sectors`: Show detailed sector performance
- `--tickers`: Show detailed ticker rankings
- `--recommendations`: Include analyst recommendations
- `--indices`: Show market indices performance
- `--timeline`: Show recent news timeline
- Default: Show all sections

### Performance Options
- `--quick`: Use fewer data sources for faster analysis
- `--verbose`: Show debug information

## Common Use Cases

### 1. Daily Market Check
```bash
python financial_analyzer.py --quick
```
**Use case**: Quick morning market sentiment check
**Output**: Overall sentiment, top performers, recommendation

### 2. Sector Analysis
```bash
python financial_analyzer.py --sectors --tickers
```
**Use case**: Identify best performing sectors and stocks
**Output**: Sector rankings, top tickers, performance metrics

### 3. Policy Impact Assessment
```bash
python financial_analyzer.py --policy-only
```
**Use case**: Check government policy impact on markets
**Output**: Policy sentiment, high-impact announcements

### 4. Investment Research
```bash
python financial_analyzer.py --recommendations --timeline
```
**Use case**: Research specific stocks with analyst data
**Output**: Analyst recommendations, recent news, price targets

### 5. Market Overview
```bash
python financial_analyzer.py --indices
```
**Use case**: Check overall market performance
**Output**: Major indices performance, market trend

## Understanding the Output

### Sentiment Scores
- **Range**: -1.0 (very negative) to +1.0 (very positive)
- **Positive**: > +0.1
- **Neutral**: -0.1 to +0.1
- **Negative**: < -0.1

### Market Recommendations
- **STRONG BUY**: High positive sentiment + strong market performance
- **BUY**: Positive sentiment + good market performance
- **HOLD**: Neutral sentiment or mixed signals
- **CAUTION**: Negative sentiment but not severe
- **SELL**: Strong negative sentiment + poor market performance

### Policy Impact Levels
- **High**: Interest rates, monetary policy, major economic announcements
- **Medium**: Banking regulations, financial stability measures
- **Low**: Minor regulatory changes
- **Minimal**: Limited market impact expected

### Sector Strength Score
- Calculated from top 3 performers in each sector
- Higher scores indicate better sentiment and performance
- Used for sector rankings

## Troubleshooting

### Common Issues

1. **"No news data available"**
   ```bash
   # Try quick mode
   python financial_analyzer.py --quick
   
   # Or market-only mode
   python financial_analyzer.py --market-only
   ```

2. **Slow performance**
   ```bash
   # Use quick mode
   python financial_analyzer.py --quick
   
   # Or reduce scope
   python financial_analyzer.py --market-only --quick
   ```

3. **Government feeds not loading**
   ```bash
   # Skip policy analysis
   python financial_analyzer.py --market-only
   ```

4. **Import errors**
   ```bash
   # Check dependencies
   pip install -r requirements.txt
   
   # Test modules
   python test_modules.py
   ```

### Debug Mode
```bash
python financial_analyzer.py --verbose
```
Shows detailed information about data fetching and processing.

## Configuration

### Customizing Analysis (config.py)

```python
# Modify these settings in config.py:

ANALYSIS_CONFIG = {
    'max_workers': 10,              # Parallel processing threads
    'articles_per_ticker': 3,       # News articles per stock
    'articles_per_feed': 5,         # Articles per government feed
    'quick_mode_tickers': 20,       # Tickers in quick mode
}

DISPLAY_CONFIG = {
    'top_tickers_count': 5,         # Top tickers to display
    'top_sectors_count': 5,         # Top sectors to display
}
```

### Adding New Tickers
Add tickers to `MAJOR_TICKERS` list in `config.py`:
```python
MAJOR_TICKERS = [
    # Add your tickers here
    'YOUR_TICKER',
    # ... existing tickers
]
```

### Adding New Sectors
Update `SECTOR_MAPPING` in `config.py`:
```python
SECTOR_MAPPING = {
    'YOUR_TICKER': 'Your_Sector',
    # ... existing mappings
}
```

## Best Practices

### 1. Regular Usage
- Run daily for market sentiment tracking
- Use `--quick` for regular checks
- Use full analysis for detailed research

### 2. Performance Optimization
- Use `--quick` for faster results
- Limit analysis scope with specific flags
- Run during off-peak hours for better data availability

### 3. Data Interpretation
- Consider both market and policy sentiment
- Look at sector trends for diversification
- Check analyst recommendations for validation
- Monitor high-impact policy news

### 4. Risk Management
- Use sentiment as one factor among many
- Don't rely solely on automated analysis
- Consider market volatility and external factors
- Always do additional research before investing

## Advanced Usage

### Combining with Other Tools
```bash
# Save output to file
python financial_analyzer.py > market_analysis.txt

# Run specific analysis and pipe to grep
python financial_analyzer.py --tickers | grep "AAPL"

# Schedule regular analysis (Linux/Mac)
crontab -e
# Add: 0 9 * * 1-5 cd /path/to/trading_tools && python financial_analyzer.py --quick
```

### Automation Ideas
- Schedule daily analysis reports
- Set up alerts for negative sentiment
- Track specific sectors or tickers
- Integrate with trading platforms (advanced)

## Support

For issues or questions:
1. Check this usage guide
2. Review the main README.md
3. Run `python test_modules.py` to verify installation
4. Check the troubleshooting section above
