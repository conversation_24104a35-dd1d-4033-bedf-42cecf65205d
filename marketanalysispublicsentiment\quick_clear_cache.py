#!/usr/bin/env python3
"""
Quick Cache Clear - Simple script to clear all cache data

This is a simplified version that just clears everything without prompts.
"""

import os
import shutil
from pathlib import Path

def quick_clear_all():
    """Quickly clear all cache data"""
    print("🧹 Quick Cache Clear - Removing all cached data...")
    
    cleared_items = []
    
    # 1. Clear cache directory
    cache_dir = Path("cache")
    if cache_dir.exists():
        file_count = len(list(cache_dir.glob("*")))
        shutil.rmtree(cache_dir)
        cleared_items.append(f"Cache directory ({file_count} files)")
    
    # 2. Clear __pycache__ directories
    pycache_count = 0
    for root, dirs, files in os.walk("."):
        if "__pycache__" in dirs:
            pycache_path = os.path.join(root, "__pycache__")
            try:
                shutil.rmtree(pycache_path)
                pycache_count += 1
            except:
                pass
    
    if pycache_count > 0:
        cleared_items.append(f"Python bytecode cache ({pycache_count} directories)")
    
    # 3. Clear any .pyc files that might be loose
    pyc_count = 0
    for root, dirs, files in os.walk("."):
        for file in files:
            if file.endswith('.pyc'):
                try:
                    os.remove(os.path.join(root, file))
                    pyc_count += 1
                except:
                    pass
    
    if pyc_count > 0:
        cleared_items.append(f"Loose .pyc files ({pyc_count} files)")
    
    # Report results
    if cleared_items:
        print("✅ Successfully cleared:")
        for item in cleared_items:
            print(f"   - {item}")
    else:
        print("📁 No cache data found to clear")
    
    print("\n💡 All cache data has been cleared!")
    print("   Next run will fetch fresh data from APIs")

if __name__ == "__main__":
    quick_clear_all()
