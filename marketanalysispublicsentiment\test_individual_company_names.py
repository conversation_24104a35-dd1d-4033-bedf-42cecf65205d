#!/usr/bin/env python3
"""
Test individual company name fetching
"""

from cached_data_fetcher import cached_get_ticker_company_name

def test_individual_company_names():
    """Test individual company name fetching"""
    test_tickers = ['GS', 'AMZN', 'ORCL', 'AAPL']
    
    print("🔍 Testing individual company name fetching...")
    
    for ticker in test_tickers:
        print(f"\n📊 Testing {ticker}:")
        company_name = cached_get_ticker_company_name(ticker)
        print(f"  Result: {company_name}")

if __name__ == "__main__":
    test_individual_company_names()
