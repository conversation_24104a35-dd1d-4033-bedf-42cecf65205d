# Financial Sentiment Analyzer Dependencies

# Core data analysis
yfinance>=0.2.18
numpy>=1.21.0
textblob>=0.17.1

# RSS feed parsing
feedparser>=6.0.10

# Date/time handling
pytz>=2023.3

# HTTP requests (usually included with Python)
requests>=2.28.0

# Standard library modules (no installation needed):
# - argparse
# - sys
# - datetime
# - concurrent.futures
# - functools
# - xml.etree.ElementTree
# - urllib.parse
# - re
