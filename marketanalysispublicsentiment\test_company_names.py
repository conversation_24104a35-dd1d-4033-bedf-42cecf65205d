#!/usr/bin/env python3
"""
Test script to verify company names are being fetched correctly
"""

import yfinance as yf
from cached_data_fetcher import cached_get_multiple_ticker_company_names
from cache_manager import cache_manager

def test_yfinance_direct():
    """Test yfinance API directly"""
    print("🔍 Testing yfinance API directly...")

    test_tickers = ['GS', 'ORCL', 'AAPL']

    for ticker in test_tickers:
        try:
            print(f"\n📊 Testing {ticker}:")
            stock = yf.Ticker(ticker)
            info = stock.info

            print(f"  longName: {info.get('longName', 'NOT_FOUND')}")
            print(f"  shortName: {info.get('shortName', 'NOT_FOUND')}")
            print(f"  displayName: {info.get('displayName', 'NOT_FOUND')}")
            print(f"  quoteType: {info.get('quoteType', 'NOT_FOUND')}")

            # Test the same logic as in cached_data_fetcher (fixed)
            company_name = (info.get('longName') or
                           info.get('shortName') or
                           info.get('displayName'))

            print(f"  Final result: {company_name if company_name else ticker}")
            print(f"  Logic check - company_name is: '{company_name}'")

        except Exception as e:
            print(f"  ❌ Error: {e}")

def test_company_names():
    """Test company name fetching for common tickers"""
    test_tickers = ['GS', 'ORCL', 'AAPL', 'MSFT', 'GOOGL', 'AMZN']

    print("\n🔍 Testing cached company name fetching...")

    company_names = cached_get_multiple_ticker_company_names(test_tickers)

    print(f"✅ Fetched company names for {len(company_names)} tickers:")
    for ticker, name in company_names.items():
        print(f"  {ticker}: {name}")

    # Test specific mappings
    expected_mappings = {
        'GS': 'Goldman Sachs Group Inc.',
        'ORCL': 'Oracle Corporation',
        'AAPL': 'Apple Inc.',
        'MSFT': 'Microsoft Corporation',
        'GOOGL': 'Alphabet Inc.',
        'AMZN': 'Amazon.com, Inc.'
    }

    print("\n🎯 Checking expected mappings:")
    for ticker, expected in expected_mappings.items():
        actual = company_names.get(ticker, 'NOT_FOUND')
        if expected.lower() in actual.lower() or actual.lower() in expected.lower():
            print(f"  ✅ {ticker}: {actual}")
        else:
            print(f"  ❌ {ticker}: Expected '{expected}', got '{actual}'")

if __name__ == "__main__":
    test_yfinance_direct()
    test_company_names()
