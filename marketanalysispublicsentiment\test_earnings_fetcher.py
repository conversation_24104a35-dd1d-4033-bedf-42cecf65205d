#!/usr/bin/env python3
"""
Test the earnings fetcher functionality
"""

from earnings_fetcher import (
    get_ticker_quarterly_earnings, 
    analyze_earnings_trends,
    get_earnings_summary_for_ticker,
    get_multiple_ticker_earnings
)
import json

def test_single_ticker():
    """Test earnings fetching for a single ticker"""
    ticker = "AAPL"
    print(f"🔍 Testing earnings fetcher for {ticker}...")
    
    # Test raw earnings data
    print("\n📊 Raw earnings data:")
    earnings_data = get_ticker_quarterly_earnings(ticker)
    
    if earnings_data:
        print(f"✅ Successfully fetched earnings for {ticker}")
        print(f"📅 Quarters available: {len(earnings_data.get('quarters', []))}")
        
        if earnings_data.get('quarters'):
            latest_quarter = earnings_data['quarters'][0]
            print(f"📈 Latest quarter: {latest_quarter['quarter']}")
            print(f"💰 Revenue: ${latest_quarter['metrics'].get('revenue', 0):,.0f}")
            print(f"💵 Net Income: ${latest_quarter['metrics'].get('net_income', 0):,.0f}")
        
        current_metrics = earnings_data.get('current_metrics', {})
        print(f"📊 Current EPS: {current_metrics.get('trailing_eps', 'N/A')}")
        print(f"📈 Revenue Growth: {current_metrics.get('revenue_growth', 'N/A')}")
    else:
        print(f"❌ No earnings data for {ticker}")
    
    # Test trends analysis
    print(f"\n📈 Trends analysis for {ticker}:")
    trends = analyze_earnings_trends(earnings_data)
    
    if trends:
        print(f"✅ Trends analysis completed")
        
        for metric, trend_data in trends.get('trends', {}).items():
            trend = trend_data['trend']
            growth = trend_data['avg_growth']
            emoji = '📈' if trend == 'improving' else '📉' if trend == 'declining' else '➡️'
            print(f"  {emoji} {metric.title()}: {trend} (avg growth: {growth:.1f}%)")
        
        performance = trends.get('performance', {}).get('overall', 'unknown')
        perf_emoji = '🟢' if performance == 'strong' else '🔴' if performance == 'weak' else '🟡'
        print(f"  {perf_emoji} Overall Performance: {performance}")
    else:
        print(f"❌ No trends analysis available")
    
    # Test comprehensive summary
    print(f"\n📋 Comprehensive summary for {ticker}:")
    summary = get_earnings_summary_for_ticker(ticker)
    
    if summary.get('status') == 'success':
        print(f"✅ Summary generated successfully")
        print(f"📊 Status: {summary['status']}")
        
        latest = summary.get('latest_quarter')
        if latest:
            print(f"📅 Latest Quarter: {latest['quarter']}")
            revenue = latest['metrics'].get('revenue', 0)
            net_income = latest['metrics'].get('net_income', 0)
            print(f"💰 Revenue: ${revenue:,.0f}")
            print(f"💵 Net Income: ${net_income:,.0f}")
            
            # Calculate profit margin
            if revenue and revenue > 0:
                margin = (net_income / revenue) * 100
                print(f"📊 Profit Margin: {margin:.1f}%")
    else:
        print(f"❌ Summary generation failed: {summary.get('status')}")

def test_multiple_tickers():
    """Test earnings fetching for multiple tickers"""
    test_tickers = ['AAPL', 'MSFT', 'GOOGL']
    print(f"\n🔍 Testing multiple tickers: {', '.join(test_tickers)}")
    
    earnings_data = get_multiple_ticker_earnings(test_tickers)
    
    print(f"📊 Results for {len(earnings_data)} tickers:")
    for ticker, data in earnings_data.items():
        if data and data.get('quarters'):
            quarters_count = len(data['quarters'])
            latest_quarter = data['quarters'][0] if data['quarters'] else {}
            revenue = latest_quarter.get('metrics', {}).get('revenue', 0)
            
            print(f"  ✅ {ticker}: {quarters_count} quarters, latest revenue: ${revenue:,.0f}")
        else:
            print(f"  ❌ {ticker}: No data available")

if __name__ == "__main__":
    test_single_ticker()
    test_multiple_tickers()
