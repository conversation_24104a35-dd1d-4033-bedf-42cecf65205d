#!/usr/bin/env python3
"""
Test script to show real earnings trends for a specific ticker
"""

from earnings_fetcher import get_earnings_summary_for_ticker

def test_real_earnings_trends(ticker="AAPL"):
    """Test earnings trends with real data"""
    
    print(f"🔍 REAL EARNINGS TRENDS EXAMPLE - {ticker}")
    print("=" * 60)
    
    # Get real earnings data
    earnings_summary = get_earnings_summary_for_ticker(ticker)
    
    if earnings_summary.get('status') != 'success':
        print(f"❌ Could not get earnings data for {ticker}")
        return
    
    # Extract the analysis
    analysis = earnings_summary.get('analysis', {})
    trends = analysis.get('trends', {})
    
    print(f"\n📊 {ticker} QUARTERLY EARNINGS DATA:")
    
    # Show the raw quarterly data
    quarters = earnings_summary.get('raw_data', {}).get('quarters', [])
    if quarters:
        print("\nLast 4 quarters:")
        for i, quarter in enumerate(quarters[:4]):
            revenue = quarter.get('metrics', {}).get('revenue', 0)
            net_income = quarter.get('metrics', {}).get('net_income', 0)
            
            revenue_str = f"${revenue/1e9:.1f}B" if revenue > 1e9 else f"${revenue/1e6:.0f}M" if revenue else "N/A"
            income_str = f"${net_income/1e9:.1f}B" if abs(net_income) > 1e9 else f"${net_income/1e6:.0f}M" if net_income else "N/A"
            
            print(f"   {quarter.get('quarter', 'Unknown')}: Revenue {revenue_str}, Net Income {income_str}")
    
    print(f"\n🧮 CALCULATED TRENDS:")
    
    # Show revenue trend
    revenue_trend = trends.get('revenue', {})
    if revenue_trend:
        trend_name = revenue_trend.get('trend', 'unknown')
        avg_growth = revenue_trend.get('avg_growth', 0)
        
        if trend_name == 'improving':
            emoji = "📈"
            color = "🟢"
        elif trend_name == 'declining':
            emoji = "📉"
            color = "🔴"
        else:
            emoji = "➡️"
            color = "🟡"
        
        print(f"   📈 Rev Trend: {color} {emoji} {trend_name.title()} ({avg_growth:+.1f}%)")
    
    # Show income trend
    income_trend = trends.get('net_income', {})
    if income_trend:
        trend_name = income_trend.get('trend', 'unknown')
        avg_growth = income_trend.get('avg_growth', 0)
        
        if trend_name == 'improving':
            emoji = "📈"
            color = "🟢"
        elif trend_name == 'declining':
            emoji = "📉"
            color = "🔴"
        else:
            emoji = "➡️"
            color = "🟡"
        
        print(f"   💰 Inc Trend: {color} {emoji} {trend_name.title()} ({avg_growth:+.1f}%)")
    
    # Show overall performance
    performance = analysis.get('performance', {})
    overall = performance.get('overall', 'unknown')
    
    if overall == 'strong':
        perf_emoji = "🟢"
        perf_color = "Strong"
    elif overall == 'weak':
        perf_emoji = "🔴"
        perf_color = "Weak"
    else:
        perf_emoji = "🟡"
        perf_color = "Mixed"
    
    print(f"   🏆 Overall: {perf_emoji} {perf_color}")
    
    print(f"\n💡 INTERPRETATION:")
    
    revenue_trend_name = trends.get('revenue', {}).get('trend', 'unknown')
    income_trend_name = trends.get('net_income', {}).get('trend', 'unknown')
    
    if revenue_trend_name == 'improving' and income_trend_name == 'improving':
        interpretation = "🚀 Excellent! Both revenue and profits are growing strongly."
    elif revenue_trend_name == 'improving' and income_trend_name == 'stable':
        interpretation = "📈 Good growth, but watch profit margins."
    elif revenue_trend_name == 'stable' and income_trend_name == 'improving':
        interpretation = "💰 Great efficiency! Making more profit from same revenue."
    elif revenue_trend_name == 'stable' and income_trend_name == 'stable':
        interpretation = "➡️ Steady business, consistent performance."
    elif revenue_trend_name == 'declining' or income_trend_name == 'declining':
        interpretation = "⚠️ Some concerns - declining trends need attention."
    else:
        interpretation = "🤔 Mixed signals - need deeper analysis."
    
    print(f"   {interpretation}")

if __name__ == "__main__":
    # Test with a few different tickers
    test_tickers = ["AAPL", "MSFT", "GOOGL"]
    
    for ticker in test_tickers:
        test_real_earnings_trends(ticker)
        print("\n" + "-" * 60 + "\n")
