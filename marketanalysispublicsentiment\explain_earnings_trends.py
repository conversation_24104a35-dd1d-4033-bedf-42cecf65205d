#!/usr/bin/env python3
"""
Earnings Trends Explanation and Example Calculator

This script explains how Rev Trend and Inc Trend are calculated
and provides examples with real data.
"""

def explain_earnings_trends():
    """Explain what Rev Trend and Inc Trend mean"""
    
    print("📊 EARNINGS TRENDS EXPLANATION")
    print("=" * 60)
    
    print("\n🔍 WHAT DO THESE MEAN?")
    print("\n📈 Rev Trend (Revenue Trend):")
    print("   - Shows whether company revenue is growing or declining")
    print("   - Based on quarter-over-quarter revenue growth")
    print("   - Indicates business expansion or contraction")
    
    print("\n💰 Inc Trend (Income Trend):")
    print("   - Shows whether company net income is growing or declining") 
    print("   - Based on quarter-over-quarter net income growth")
    print("   - Indicates profitability improvement or deterioration")
    
    print("\n🧮 HOW ARE THEY CALCULATED?")
    print("\nStep 1: Quarter-over-Quarter Growth Rate")
    print("   Formula: ((Current Quarter - Previous Quarter) / |Previous Quarter|) × 100")
    
    print("\nStep 2: Average Recent Growth")
    print("   - Takes the last 2 quarters of growth rates")
    print("   - Calculates the average growth rate")
    
    print("\nStep 3: Trend Classification")
    print("   📈 IMPROVING: Average growth > +5%")
    print("   📉 DECLINING: Average growth < -5%") 
    print("   ➡️  STABLE:   Average growth between -5% and +5%")

def calculate_example_trends():
    """Show example calculations with sample data"""
    
    print("\n" + "=" * 60)
    print("📋 EXAMPLE CALCULATION")
    print("=" * 60)
    
    # Example data for a fictional company
    quarters_data = [
        {"quarter": "Q1 2025", "revenue": 155.7, "net_income": 17.1},  # Most recent
        {"quarter": "Q4 2024", "revenue": 154.9, "net_income": 15.7},
        {"quarter": "Q3 2024", "revenue": 147.8, "net_income": 14.2},
        {"quarter": "Q2 2024", "revenue": 143.1, "net_income": 13.8}
    ]
    
    print(f"\n📊 Sample Company Quarterly Data (in billions):")
    for q in quarters_data:
        print(f"   {q['quarter']}: Revenue ${q['revenue']}B, Net Income ${q['net_income']}B")
    
    print(f"\n🧮 REVENUE TREND CALCULATION:")
    
    # Calculate revenue growth rates
    revenue_growth_rates = []
    for i in range(len(quarters_data) - 1):
        current = quarters_data[i]['revenue']
        previous = quarters_data[i + 1]['revenue']
        growth_rate = ((current - previous) / abs(previous)) * 100
        revenue_growth_rates.append(growth_rate)
        
        print(f"   {quarters_data[i]['quarter']} vs {quarters_data[i+1]['quarter']}:")
        print(f"   ({current} - {previous}) / {previous} × 100 = {growth_rate:+.1f}%")
    
    # Calculate average of last 2 quarters
    recent_revenue_growth = revenue_growth_rates[:2]  # Last 2 quarters
    avg_revenue_growth = sum(recent_revenue_growth) / len(recent_revenue_growth)
    
    print(f"\n   Average of last 2 quarters: ({recent_revenue_growth[0]:+.1f}% + {recent_revenue_growth[1]:+.1f}%) / 2 = {avg_revenue_growth:+.1f}%")
    
    # Determine revenue trend
    if avg_revenue_growth > 5:
        revenue_trend = "📈 IMPROVING"
    elif avg_revenue_growth < -5:
        revenue_trend = "📉 DECLINING"
    else:
        revenue_trend = "➡️ STABLE"
    
    print(f"   Revenue Trend: {revenue_trend}")
    
    print(f"\n🧮 NET INCOME TREND CALCULATION:")
    
    # Calculate net income growth rates
    income_growth_rates = []
    for i in range(len(quarters_data) - 1):
        current = quarters_data[i]['net_income']
        previous = quarters_data[i + 1]['net_income']
        growth_rate = ((current - previous) / abs(previous)) * 100
        income_growth_rates.append(growth_rate)
        
        print(f"   {quarters_data[i]['quarter']} vs {quarters_data[i+1]['quarter']}:")
        print(f"   ({current} - {previous}) / {previous} × 100 = {growth_rate:+.1f}%")
    
    # Calculate average of last 2 quarters
    recent_income_growth = income_growth_rates[:2]  # Last 2 quarters
    avg_income_growth = sum(recent_income_growth) / len(recent_income_growth)
    
    print(f"\n   Average of last 2 quarters: ({recent_income_growth[0]:+.1f}% + {recent_income_growth[1]:+.1f}%) / 2 = {avg_income_growth:+.1f}%")
    
    # Determine income trend
    if avg_income_growth > 5:
        income_trend = "📈 IMPROVING"
    elif avg_income_growth < -5:
        income_trend = "📉 DECLINING"
    else:
        income_trend = "➡️ STABLE"
    
    print(f"   Income Trend: {income_trend}")
    
    print(f"\n📊 FINAL RESULTS:")
    print(f"   📈 Rev Trend: {revenue_trend} ({avg_revenue_growth:+.1f}%)")
    print(f"   💰 Inc Trend: {income_trend} ({avg_income_growth:+.1f}%)")

def explain_interpretation():
    """Explain how to interpret the trends"""
    
    print("\n" + "=" * 60)
    print("🎯 HOW TO INTERPRET TRENDS")
    print("=" * 60)
    
    print("\n🟢 POSITIVE SCENARIOS:")
    print("   📈 Rev: Improving + 💰 Inc: Improving")
    print("   → Company is growing revenue AND becoming more profitable")
    print("   → Strong business performance")
    
    print("\n   📈 Rev: Improving + 💰 Inc: Stable")
    print("   → Company is growing but profitability is steady")
    print("   → Good growth, watch profit margins")
    
    print("\n🟡 MIXED SCENARIOS:")
    print("   📈 Rev: Stable + 💰 Inc: Improving")
    print("   → Revenue flat but becoming more efficient/profitable")
    print("   → Good cost management")
    
    print("\n   📈 Rev: Stable + 💰 Inc: Stable")
    print("   → Consistent but not growing business")
    print("   → Mature company or market conditions")
    
    print("\n🔴 CONCERNING SCENARIOS:")
    print("   📈 Rev: Declining + 💰 Inc: Declining")
    print("   → Business is shrinking and becoming less profitable")
    print("   → Potential problems or market challenges")
    
    print("\n   📈 Rev: Improving + 💰 Inc: Declining")
    print("   → Growing revenue but profit margins are shrinking")
    print("   → May indicate pricing pressure or rising costs")

def explain_dashboard_display():
    """Explain how trends appear in the dashboard"""
    
    print("\n" + "=" * 60)
    print("📱 IN THE DASHBOARD")
    print("=" * 60)
    
    print("\nYou'll see trends displayed like this:")
    print("\n📈 Rev Trend: 📈 Improving (+8.9%)")
    print("💵 Inc Trend: 📈 Improving (+16.3%)")
    
    print("\nBreaking this down:")
    print("   📈 = Emoji indicator (📈 improving, 📉 declining, ➡️ stable)")
    print("   'Improving' = Trend classification")
    print("   '(+8.9%)' = Average growth rate over last 2 quarters")
    
    print("\n🎨 Color Coding:")
    print("   🟢 Green = Improving trends (good)")
    print("   🔴 Red = Declining trends (concerning)")
    print("   🟡 Yellow = Stable trends (neutral)")

def main():
    """Main explanation function"""
    explain_earnings_trends()
    calculate_example_trends()
    explain_interpretation()
    explain_dashboard_display()
    
    print("\n" + "=" * 60)
    print("✅ SUMMARY")
    print("=" * 60)
    print("\n📈 Rev Trend = Revenue growth direction over last 2 quarters")
    print("💰 Inc Trend = Net income growth direction over last 2 quarters")
    print("\nBoth help you quickly assess if a company is:")
    print("   📈 Growing (improving)")
    print("   📉 Shrinking (declining)")  
    print("   ➡️ Steady (stable)")
    print("\nThis gives you instant insight into business momentum! 🚀")

if __name__ == "__main__":
    main()
